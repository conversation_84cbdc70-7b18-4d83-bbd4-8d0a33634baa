{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAYe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB,wCAA2C;YACzC;QACF;;QAEA,MAAM;QAEN,kCAAkC;QAClC,MAAM;QAYN,oCAAoC;QACpC,MAAM;QAUN,6BAA6B;QAC7B,MAAM;QAWN,mCAAmC;QACnC,MAAM;QAeN,+BAA+B;QAC/B,MAAM;QASN,SAAS;QACT,MAAM;QA4BN,cAAc;QACd,MAAM;IAYR,GAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;AAGO,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport PerformanceMonitor from '@/components/seo/PerformanceMonitor';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <PerformanceMonitor />\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,UAAkB;;;;;0BAGnB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\nimport { useLike } from '@/contexts/LikeContext';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean;\n  showCount?: boolean; // 是否显示点赞数量\n  size?: 'sm' | 'md' | 'lg'; // 按钮大小\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false,\n  showCount = true,\n  size = 'md'\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const { getToolState, initializeToolState, toggleLike } = useLike();\n\n  // 获取当前工具的状态\n  const toolState = getToolState(toolId);\n\n  // 初始化工具状态\n  useEffect(() => {\n    initializeToolState(toolId, initialLikes, initialLiked);\n  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环\n\n  // 处理点赞点击\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (toolState.loading) return;\n\n    // 记录操作前的状态\n    const wasLiked = toolState.liked;\n\n    // 执行点赞操作\n    const success = await toggleLike(toolId, isInLikedPage);\n\n    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调\n    if (success && isInLikedPage && wasLiked && onUnlike) {\n      onUnlike(toolId);\n    }\n  };\n\n  // 根据size确定样式\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return {\n          button: 'p-1.5',\n          icon: 'h-4 w-4',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          button: 'p-3',\n          icon: 'h-6 w-6',\n          text: 'text-lg'\n        };\n      default: // md\n        return {\n          button: 'p-2',\n          icon: 'h-5 w-5',\n          text: 'text-base'\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={toolState.loading}\n      className={`\n        ${sizeClasses.button}\n        inline-flex items-center space-x-1\n        ${toolState.liked\n          ? 'text-red-500 hover:text-red-600'\n          : 'text-gray-400 hover:text-red-500'\n        }\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      `}\n      title={toolState.liked ? '取消点赞' : '点赞'}\n    >\n      {toolState.loading ? (\n        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />\n      ) : toolState.liked ? (\n        <FaHeart className={sizeClasses.icon} />\n      ) : (\n        <FaRegHeart className={sizeClasses.icon} />\n      )}\n      {showCount && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {toolState.likes}\n        </span>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAkBe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACrB,YAAY,IAAI,EAChB,OAAO,IAAI,EACK;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhE,YAAY;IACZ,MAAM,YAAY,aAAa;IAE/B,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB,QAAQ,cAAc;IAC5C,GAAG;QAAC;QAAQ;QAAc;KAAa,GAAG,iCAAiC;IAE3E,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,UAAU,OAAO,EAAE;QAEvB,WAAW;QACX,MAAM,WAAW,UAAU,KAAK;QAEhC,SAAS;QACT,MAAM,UAAU,MAAM,WAAW,QAAQ;QAEzC,kCAAkC;QAClC,IAAI,WAAW,iBAAiB,YAAY,UAAU;YACpD,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QACC,SAAS;QACT,UAAU,UAAU,OAAO;QAC3B,WAAW,CAAC;QACV,EAAE,YAAY,MAAM,CAAC;;QAErB,EAAE,UAAU,KAAK,GACb,oCACA,mCACH;;;MAGH,CAAC;QACD,OAAO,UAAU,KAAK,GAAG,SAAS;;YAEjC,UAAU,OAAO,iBAChB,8OAAC;gBAAI,WAAW,GAAG,YAAY,IAAI,CAAC,oEAAoE,CAAC;;;;;uBACvG,UAAU,KAAK,iBACjB,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAW,YAAY,IAAI;;;;;qCAEpC,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAW,YAAY,IAAI;;;;;;YAExC,2BACC,8OAAC;gBAAK,WAAW,GAAG,YAAY,IAAI,CAAC,YAAY,CAAC;0BAC/C,UAAU,KAAK;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n  onError?: () => void;\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n  onError,\n}: OptimizedImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    setHasError(true);\n    setIsLoading(false);\n    setImgSrc(fallbackSrc);\n    onError?.();\n  };\n\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n\n  // 生成模糊占位符\n  const generateBlurDataURL = (w: number = 10, h: number = 10) => {\n    const canvas = document.createElement('canvas');\n    canvas.width = w;\n    canvas.height = h;\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.fillStyle = '#f3f4f6';\n      ctx.fillRect(0, 0, w, h);\n    }\n    return canvas.toDataURL();\n  };\n\n  const imageProps = {\n    src: imgSrc,\n    alt,\n    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n    onError: handleError,\n    onLoad: handleLoad,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'cover' }}\n        />\n        {isLoading && (\n          <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Image\n        {...imageProps}\n        width={width}\n        height={height}\n      />\n      {isLoading && (\n        <div \n          className=\"absolute inset-0 bg-gray-200 animate-pulse\"\n          style={{ width, height }}\n        />\n      )}\n    </div>\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 64, height: 64 },\n  toolLogoLarge: { width: 128, height: 128 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '64px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAoBe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACvC,OAAO,EACa;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,YAAY;QACZ,aAAa;QACb,UAAU;QACV;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC,IAAY,EAAE,EAAE,IAAY,EAAE;QACzD,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,KAAK;YACP,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QACA,OAAO,OAAO,SAAS;IACzB;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,GAAG,UAAU,CAAC,EAAE,YAAY,cAAc,cAAc,gCAAgC,CAAC;QACpG,SAAS;QACT,QAAQ;QACR;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACH,GAAG,UAAU;oBACd,IAAI;oBACJ,OAAO;wBAAE,WAAW;oBAAQ;;;;;;gBAE7B,2BACC,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,OAAO;gBACP,QAAQ;;;;;;YAET,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE;oBAAO;gBAAO;;;;;;;;;;;;AAKjC;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAK,QAAQ;IAAI;IACzC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from './ui/OptimizedImage';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false }) => {\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <OptimizedImage\n                src={tool.logo}\n                alt={`${tool.name} logo`}\n                width={ImageSizes.toolLogo.width}\n                height={ImageSizes.toolLogo.height}\n                className=\"rounded-lg object-cover\"\n                sizes={ResponsiveSizes.toolLogo}\n                placeholder=\"blur\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getToolPricingColor(tool.pricing)}`}>\n                {getToolPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              initialLiked={isInLikedPage} // 在收藏页面，所有工具都应该是已点赞状态\n              onLoginRequired={onLoginRequired}\n              onUnlike={onUnlike}\n              isInLikedPage={isInLikedPage}\n            />\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AA2BA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,gBAAgB,KAAK,EAAE;IAEnG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,8OAAC,0IAAA,CAAA,UAAc;oCACb,KAAK,KAAK,IAAI;oCACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oCACxB,OAAO,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;oCAChC,QAAQ,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;oCAClC,WAAU;oCACV,OAAO,0IAAA,CAAA,kBAAe,CAAC,QAAQ;oCAC/B,aAAY;;;;;yDAGd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;sDAC5H,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKtC,8OAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yIAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,cAAc;oCACd,iBAAiB;oCACjB,UAAU;oCACV,eAAe;;;;;;8CAEjB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ,MAAM,eAAe,iEAAwC;AAqI7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts"], "sourcesContent": ["// AI工具分类的统一配置文件\n// 这个文件包含所有分类的完整信息，确保整个应用中的一致性\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 完整的分类配置\nexport const CATEGORY_CONFIGS: CategoryConfig[] = [\n  {\n    slug: 'text-generation',\n    name: '文本生成',\n    description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    name: '图像生成',\n    description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    name: '代码生成',\n    description: '智能代码生成和编程辅助工具，提高开发效率',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    name: '数据分析',\n    description: '数据分析和可视化工具，帮助洞察数据价值',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    name: '音频处理',\n    description: '音频处理、语音合成、音乐生成等音频AI工具',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    name: '视频编辑',\n    description: '视频生成、编辑、剪辑等视频处理AI工具',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    name: '语言翻译',\n    description: '多语言翻译和本地化AI工具',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    name: '搜索引擎',\n    description: '智能搜索和信息检索AI工具',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    name: '教育学习',\n    description: '教育培训和学习辅助AI工具',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    name: '营销工具',\n    description: '数字营销和推广AI工具',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    name: '生产力工具',\n    description: '提高工作效率的AI工具',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    name: '客户服务',\n    description: '客户支持和服务AI工具',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 生成分类选项（用于下拉框等）\nexport const CATEGORY_OPTIONS: CategoryOption[] = CATEGORY_CONFIGS.map(config => ({\n  value: config.slug,\n  label: config.name\n}));\n\n// 包含\"所有分类\"选项的分类选项\nexport const CATEGORY_OPTIONS_WITH_ALL: CategoryOption[] = [\n  { value: '', label: '所有分类' },\n  ...CATEGORY_OPTIONS\n];\n\n// 分类标签映射（slug -> 名称）\nexport const CATEGORY_LABELS: Record<string, string> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config.name;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\n// 分类元数据映射（slug -> 完整配置）\nexport const CATEGORY_METADATA: Record<string, CategoryConfig> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  },\n  {} as Record<string, CategoryConfig>\n);\n\n// 获取分类配置的辅助函数\nexport const getCategoryConfig = (slug: string): CategoryConfig | undefined => {\n  return CATEGORY_METADATA[slug];\n};\n\n// 获取分类名称的辅助函数\nexport const getCategoryName = (slug: string): string => {\n  return CATEGORY_LABELS[slug] || slug;\n};\n\n// 验证分类是否存在的辅助函数\nexport const isValidCategory = (slug: string): boolean => {\n  return slug in CATEGORY_METADATA;\n};\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_CONFIGS.map(config => config.slug);\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,8BAA8B;;;;;;;;;;;;AAgBvB,MAAM,mBAAqC;IAChD;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,mBAAqC,iBAAiB,GAAG,CAAC,CAAA,SAAU,CAAC;QAChF,OAAO,OAAO,IAAI;QAClB,OAAO,OAAO,IAAI;IACpB,CAAC;AAGM,MAAM,4BAA8C;IACzD;QAAE,OAAO;QAAI,OAAO;IAAO;OACxB;CACJ;AAGM,MAAM,kBAA0C,iBAAiB,MAAM,CAC5E,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI;IAC9B,OAAO;AACT,GACA,CAAC;AAII,MAAM,oBAAoD,iBAAiB,MAAM,CACtF,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GACA,CAAC;AAII,MAAM,oBAAoB,CAAC;IAChC,OAAO,iBAAiB,CAAC,KAAK;AAChC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,eAAe,CAAC,KAAK,IAAI;AAClC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,QAAQ;AACjB;AAGO,MAAM,iBAAiB,iBAAiB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI", "debugId": null}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport ToolCard from '@/components/ToolCard';\nimport { apiClient, Tool } from '@/lib/api';\nimport { CATEGORY_LABELS } from '@/constants/categories';\nimport {\n  Heart,\n  ArrowLeft,\n  Search,\n  Filter\n} from 'lucide-react';\n\n// 使用统一的分类标签映射\nconst categoryLabels = CATEGORY_LABELS;\n\nexport default function LikedToolsPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [likedTools, setLikedTools] = useState<Tool[]>([]);\n  const [filteredTools, setFilteredTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/');\n      return;\n    }\n    \n    if (status === 'authenticated') {\n      fetchLikedTools();\n    }\n  }, [status, router]);\n\n  useEffect(() => {\n    filterTools();\n  }, [likedTools, searchQuery, selectedCategory]);\n\n  const fetchLikedTools = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // 获取用户收藏的工具列表\n      const response = await apiClient.getLikedTools({ limit: 100 });\n\n      if (response.success && response.data) {\n        setLikedTools(response.data.tools);\n      } else {\n        setError(response.error || '获取收藏列表失败');\n      }\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterTools = () => {\n    let filtered = likedTools;\n\n    // 按搜索关键词过滤\n    if (searchQuery) {\n      filtered = filtered.filter(tool =>\n        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        tool.description.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    // 按分类过滤\n    if (selectedCategory !== 'all') {\n      filtered = filtered.filter(tool => tool.category === selectedCategory);\n    }\n\n    setFilteredTools(filtered);\n  };\n\n  const handleUnlike = async (toolId: string) => {\n    try {\n      setLikedTools(prev => prev.filter(tool => tool._id !== toolId));\n    } catch (error) {\n      console.error('Error unliking tool:', error);\n    }\n  };\n\n  // 获取所有分类\n  const categories = Array.from(new Set(likedTools.map(tool => tool.category)));\n\n  if (status === 'loading' || loading) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <LoadingSpinner size=\"lg\" className=\"py-20\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8\">\n          <div>\n            <div className=\"flex items-center mb-2\">\n              <Link\n                href=\"/profile\"\n                className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n              >\n                <ArrowLeft className=\"h-5 w-5\" />\n              </Link>\n              <h1 className=\"text-3xl font-bold text-gray-900\">我的收藏</h1>\n            </div>\n            <p className=\"text-lg text-gray-600\">您收藏的AI工具 ({likedTools.length})</p>\n          </div>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Search className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索收藏的工具...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            {/* Category Filter */}\n            <div className=\"sm:w-48\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Filter className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">所有分类</option>\n                  {categories.map(category => (\n                    <option key={category} value={category}>\n                      {categoryLabels[category] || category}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {/* Tools Grid */}\n        {filteredTools.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredTools.map((tool) => (\n              <ToolCard\n                key={tool._id}\n                tool={tool}\n                onUnlike={handleUnlike}\n                isInLikedPage={true}\n              />\n            ))}\n          </div>\n        ) : (\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center\">\n            <div className=\"text-gray-400 mb-4\">\n              <Heart className=\"h-12 w-12 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchQuery || selectedCategory !== 'all' ? '没有找到匹配的工具' : '还没有收藏任何工具'}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {searchQuery || selectedCategory !== 'all' \n                ? '尝试调整搜索条件或筛选器'\n                : '开始探索并收藏您喜欢的AI工具吧！'\n              }\n            </p>\n            {(!searchQuery && selectedCategory === 'all') && (\n              <Link\n                href=\"/tools\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n              >\n                浏览工具\n              </Link>\n            )}\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;;AAmBA,cAAc;AACd,MAAM,iBAAiB,8HAAA,CAAA,kBAAe;AAEvB,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,iBAAiB;YAC9B;QACF;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAY;QAAa;KAAiB;IAE9C,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,SAAS;YAET,cAAc;YACd,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;gBAAE,OAAO;YAAI;YAE5D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,cAAc,SAAS,IAAI,CAAC,KAAK;YACnC,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;QAEf,WAAW;QACX,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEnE;QAEA,QAAQ;QACR,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACvD;QAEA,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,SAAS;IACT,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAE1E,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;IAI5C;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAE,WAAU;;oCAAwB;oCAAW,WAAW,MAAM;oCAAC;;;;;;;;;;;;;;;;;;8BAKtE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;wDAAsB,OAAO;kEAC3B,cAAc,CAAC,SAAS,IAAI;uDADlB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWxB,uBACC,8OAAC,kIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;oBACxB,WAAU;;;;;;gBAKb,cAAc,MAAM,GAAG,kBACtB,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,8HAAA,CAAA,UAAQ;4BAEP,MAAM;4BACN,UAAU;4BACV,eAAe;2BAHV,KAAK,GAAG;;;;;;;;;yCAQnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCACX,eAAe,qBAAqB,QAAQ,cAAc;;;;;;sCAE7D,8OAAC;4BAAE,WAAU;sCACV,eAAe,qBAAqB,QACjC,iBACA;;;;;;wBAGJ,CAAC,eAAe,qBAAqB,uBACrC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}