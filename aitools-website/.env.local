# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/aitools

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3002
NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production

# Application Settings
NEXT_PUBLIC_APP_NAME=AI Tools Directory
NEXT_PUBLIC_APP_URL=http://localhost:3002

# API Configuration
API_BASE_URL=http://localhost:3002/api
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002/api

# Email Configuration (for verification codes)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=email-smtp.us-east-1.amazonaws.com
EMAIL_PORT=587
EMAIL_USER=AKIA3ISBVRA5A3CYOY44
EMAIL_PASS=BMLt3zzAXGj/mm/h9nLW5otRZaTBsKFx3OPau0uRFE4u

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51K8G6vHZxvPjnxC8cwIdVRXUaLB4pFCoKnuDsUL8CDEctyBSkjskIDkq1yNSqwgKAr1fYaeOqdbu4Q7MEOib4mpn00WIlgYsoI
# STRIPE_SECRET_KEY=***********************************************************************************************************
# STRIPE_WEBHOOK_SECRET=whsec_TCzVwaErSjlFfUTqteMvF3GO4A7VDWcc

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM
STRIPE_SECRET_KEY=sk_test_51K8G6vHZxvPjnxC8lZOtPnLUtmR5CvsfV9TlaCSmiANDuNNvXaeMGdhn1UmXmFe9h2K3ubhvrDIIvshtmYVJZXn900jCvfPsgh
STRIPE_WEBHOOK_SECRET=we_1Nq8llHZxvPjnxC8m2GTwzWY

# dev_stripeKeys: {
#         secret_key: 'sk_test_51K8G6vHZxvPjnxC8lZOtPnLUtmR5CvsfV9TlaCSmiANDuNNvXaeMGdhn1UmXmFe9h2K3ubhvrDIIvshtmYVJZXn900jCvfPsgh',
#         publishable_key: 'pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM',
#         webhook_secret: 'we_1Nq8llHZxvPjnxC8m2GTwzWY'
#     },